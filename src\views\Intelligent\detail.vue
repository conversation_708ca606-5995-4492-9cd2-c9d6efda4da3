<script setup lang="ts">
  import { ref } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { LeftOutlined } from '@ant-design/icons-vue';
  import { message } from 'ant-design-vue';
  import { publishAgent } from '@/api/agent';
  import ConfigTab from './Components/ConfigTab.vue';
  import PublishTab from './Components/PublishTab.vue';

  const router = useRouter();
  const route = useRoute();
  const updated_at = ref('');
  const currentTab = ref('config');
  const appName = ref('');

  const handleBack = () => {
    router.back();
  };

  const handleAutoSaveTimeUpdate = (time: string) => {
    updated_at.value = time;
  };

  const handleAppNameUpdate = (name: string) => {
    appName.value = name;
  };

  const confirmPublish = async () => {
    try {
      await publishAgent(String(route.params.id));
      message.success('已提交发布');
      router.back();
    } catch {
      // 处理错误
    }
  };
</script>

<template>
  <div class="header text-18px">
    <div>
      <LeftOutlined @click="handleBack" />
      <span class="m-l-10px">{{ appName }}</span>
    </div>
    <a-radio-group v-model:value="currentTab" class="mr-10px radio-group-center">
        <a-radio-button value="config">配置</a-radio-button>
        <a-radio-button value="publish">发布</a-radio-button>
      </a-radio-group>
    <div v-if="currentTab === 'config'" class="flex text-14px text-#797979">
      <div class="leading-[32px] mr-10px">自动保存于：{{ updated_at }}</div>

      <a-button type="primary" @click="confirmPublish">发布</a-button>
    </div>
  </div>
  <div class="container">
    <keep-alive>
      <ConfigTab
        v-if="currentTab === 'config'"
        :agent-id="String(route.params.id)"
        @auto-save-time-update="handleAutoSaveTimeUpdate"
        @app-name-update="handleAppNameUpdate"
        @publish-request="confirmPublish"
      />
      <PublishTab
        v-else-if="currentTab === 'publish'"
        :agent-id="String(route.params.id)"
        @publish-request="confirmPublish"
      />
    </keep-alive>
  </div>
</template>

<style scoped lang="less">
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 10px;
    border-bottom: 1px solid #ccc;
    position: relative;
    height: 42px;
  }
  .container {
    height: calc(100% - 40px);
  }
  .radio-group-center {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }
</style>
